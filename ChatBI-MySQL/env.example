# MySQL 配置项
DB_HOST=rr-bp19zkvz53m17h8qsmo.mysql.rds.aliyuncs.com
DB_PORT=3306
DB_USER=xianmu_ai
DB_PASSWORD=Xianmu_ai
DB_NAME=xianmudb
# 超时时间：秒数
DB_CONNECT_TIMEOUT=2

# --- OpenAI API Configuration ---
XM_OPENAI_API_KEY=sk-4wXuSpwi8cOdruDM603b2aC1Ef864709A5E4550bB17f16A9
# Optional: If using a proxy or different base URL for OpenAI API
OPENAI_API_BASE=https://test-one-api.summerfarm.top/v1
OPENAI_MODEL=deepseek-v3-250324

# --- RAG Configuration ---
TOP_TABLE_CNT=20

# --- ChatBI Configuration ---
# 当使用本地localhost时，请使用这个：
# CHAT_BI_HOST_NAME=http://127.0.0.1:5700

# 默认使用这个：
CHAT_BI_HOST_NAME=https://chat-bi.summerfarm.net

# --- Flask Configuration (Optional) ---
FLASK_ENV=production # Set to 'production' for deployment
FLASK_DEBUG=0 # Enable debug mode (1) or disable (0)

# SQL返回的行数大于这个值时，默认上传到飞书
MIN_ROWS_TO_IMPORT=20

# 强制刷新token的时间间隔（分钟）
MINUTES_TO_FORCE_REFRESH_TOKEN=10

# 飞书应用的 App ID 和 App Secret
FEISHU_APP_ID=cli_a450bff26fbbd00d
FEISHU_APP_SECRET=uQolgem8B8fwuTlsER0bZdUe7xZjueHU

# 是否启用飞书消息处理
ENABLE_BOT_MESSAGE_PROCESSING=true

# 应用根目录(解决nginx部署问题)
APPLICATION_ROOT=

# 如果超过400行，则对喂给AI的数据进行截断
MIN_ROWS_TO_CUTOFF=400

# 如果超过20行，则自动上传到飞书多维表格
MIN_ROWS_TO_IMPORT=20

# Dashboard的管理员列表
DASHBOARD_ADMINS=唐鹏,康凯,王剑锋,施正豪,江涛,李茂源,李钦,李茂源

# 飞书会话超时时间（分钟），超过这个时间，会话将被终止，无法继续发送消息
STREAM_TIMEOUT_MINUTES=9

# Bad Case通知群聊ID（可选）
# 当用户标记对话为Bad Case时，会发送通知到此群聊
# 如果不配置此项，则不会发送通知
# BAD_CASE_NOTIFICATION_CHAT_ID=oc_61c2a621535eacb60c33711c09231781