#!/usr/bin/env python3
"""
Bad Case 飞书通知功能使用示例

这个示例展示了如何使用Bad Case通知功能。
"""

import os
import sys
from dotenv import load_dotenv

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 加载环境变量
load_dotenv()

def example_basic_usage():
    """基本使用示例"""
    print("📝 基本使用示例")
    print("=" * 50)
    
    from src.services.chatbot.bad_case_service import mark_bad_case
    
    # 示例1: 标记对话为Bad Case（会触发通知）
    print("1. 标记对话为Bad Case:")
    success = mark_bad_case(
        conversation_id="example_conv_001",
        is_bad_case=True,
        user_name="张三"
    )
    print(f"   结果: {'成功' if success else '失败'}")
    
    # 示例2: 取消Bad Case标记（不会触发通知）
    print("\n2. 取消Bad Case标记:")
    success = mark_bad_case(
        conversation_id="example_conv_001",
        is_bad_case=False,
        user_name="张三"
    )
    print(f"   结果: {'成功' if success else '失败'}")
    
    # 示例3: 不提供用户名（通知中显示"用户: 未知"）
    print("\n3. 不提供用户名:")
    success = mark_bad_case(
        conversation_id="example_conv_002",
        is_bad_case=True
    )
    print(f"   结果: {'成功' if success else '失败'}")

def example_api_usage():
    """API使用示例"""
    print("\n📡 API使用示例")
    print("=" * 50)
    
    print("通过API标记Bad Case时，系统会自动获取用户信息：")
    print("""
POST /api/mark_conversation_as_bad_case
Content-Type: application/json

{
    "conversation_id": "conv_12345"
}
    """)
    
    print("系统会：")
    print("1. 从session中获取用户信息")
    print("2. 调用mark_bad_case函数")
    print("3. 装饰器自动发送飞书通知")

def example_configuration():
    """配置示例"""
    print("\n⚙️ 配置示例")
    print("=" * 50)
    
    print("1. 在.env文件中添加配置：")
    print("   BAD_CASE_NOTIFICATION_CHAT_ID=oc_xxxxxxxxxxxxxxxxxxxxxxxx")
    
    print("\n2. 获取群聊ID的方法：")
    print("   - 在飞书中创建群聊")
    print("   - 添加机器人到群聊")
    print("   - 通过飞书开放平台API获取chat_id")
    
    print("\n3. 当前配置状态：")
    chat_id = os.getenv("BAD_CASE_NOTIFICATION_CHAT_ID")
    if chat_id:
        print(f"   ✅ 已配置群聊ID: {chat_id}")
    else:
        print("   ❌ 未配置群聊ID")

def example_notification_format():
    """通知格式示例"""
    print("\n📨 通知消息格式示例")
    print("=" * 50)
    
    print("发送到飞书群聊的消息格式：")
    print("""
🚨 Bad Case 标记通知

用户: 张三
对话ID: conv_12345
标记时间: 2024-01-15 14:30:25

请相关人员及时关注和处理。
    """)

def example_error_handling():
    """错误处理示例"""
    print("\n🛠️ 错误处理示例")
    print("=" * 50)
    
    print("装饰器会处理以下错误情况：")
    print("1. 未配置群聊ID - 跳过发送通知")
    print("2. 网络错误 - 记录错误日志，不影响主功能")
    print("3. 权限错误 - 记录错误日志，不影响主功能")
    print("4. API限流 - 记录错误日志，不影响主功能")
    
    print("\n错误日志示例：")
    print("INFO: 未配置Bad Case通知群聊ID，跳过发送通知")
    print("WARNING: 发送Bad Case通知失败，对话ID: conv_12345")
    print("ERROR: 发送Bad Case通知时发生异常: [详细错误信息]")

def main():
    """主函数"""
    print("🚨 Bad Case 飞书通知功能使用示例")
    print("=" * 60)
    
    # 基本使用示例
    example_basic_usage()
    
    # API使用示例
    example_api_usage()
    
    # 配置示例
    example_configuration()
    
    # 通知格式示例
    example_notification_format()
    
    # 错误处理示例
    example_error_handling()
    
    print("\n" + "=" * 60)
    print("💡 提示：")
    print("- 运行 python test_bad_case_notification.py 来测试功能")
    print("- 查看 docs/bad_case_notification.md 获取详细文档")
    print("- 配置 BAD_CASE_NOTIFICATION_CHAT_ID 环境变量启用通知")

if __name__ == "__main__":
    main()
