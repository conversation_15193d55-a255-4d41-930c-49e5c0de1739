"""
API endpoints for managing conversation history.
"""
from flask import request, jsonify, session, Blueprint

from src.services.auth.user_login_with_feishu import login_required
from src.services.chatbot.history_service import (
    get_user_conversations,
    delete_user_conversation,
    check_conversation_ownership, 
    get_conversation_messages,
    get_conversation_count
)
from src.services.chatbot.bad_case_service import mark_bad_case
from src.utils.logger import logger
from src.utils.user_utils import get_valid_user_email

# Create a Blueprint for history endpoints
# Using url_prefix='' to maintain original URL paths
history_bp = Blueprint('history', __name__, url_prefix='')


@history_bp.route('/api/history', methods=['GET'])
@login_required
def get_history():
    """
    GET /api/history endpoint that fetches chatbot history for the logged-in user with pagination,
    或通过 chat 参数单独获取某条会话详情（仅限登录用户）。
    """
    user_info = session.get("user_info")
    username = user_info.get("name")
    email = get_valid_user_email(user_info)
    if not username or not email:
        return jsonify({"error": "用户信息未在 session 中找到"}), 401

    chat = request.args.get('chat')
    if chat:
        # 有 chat 参数，查单条会话内容
        try:
            # 校验此会话是否属于当前用户
            if not check_conversation_ownership(username, email, chat):
                return jsonify({"error": "没有权限访问该会话或会话不存在"}), 404
            messages = get_conversation_messages(username, email, chat)
            return jsonify({
                "conversation": messages
            })
        except Exception as e:
            logger.error(f"Error loading conversation {chat} for {username}: {e}")
            return jsonify({"error": "加载会话失败"}), 500
    else:
        # 分页获取全部历史
        # 获取分页参数
        try:
            limit = int(request.args.get('limit', 20))  # 默认 20
            offset = int(request.args.get('offset', 0))  # 默认 0
        except ValueError:
            return jsonify({"error": "limit 或 offset 参数无效，必须为整数"}), 400

        if limit <= 0 or offset < 0:
            return jsonify({"error": "limit 必须为正数，offset 必须为非负数"}), 400

        try:
            # 获取当前用户的全部会话数量
            total_count = get_conversation_count(username, email)
            history_data = get_user_conversations(username, email, limit=limit, offset=offset)
            return jsonify({
                "history": history_data,
                "total_count": total_count
            })
        except Exception as e:
            logger.error(f"加载历史数据出错：{username} (limit={limit}, offset={offset}): {e}")
            return jsonify({"error": "加载历史数据失败"}), 500


@history_bp.route('/api/history/<conversation_id>', methods=['DELETE'])
@login_required
def delete_history_conversation(conversation_id):
    """
    DELETE /api/history/<conversation_id> endpoint that deletes a specific conversation history.
    """
    user_info = session.get("user_info")
    username = user_info.get("name")
    email = get_valid_user_email(user_info)
    if not username or not email:
        return jsonify({"error": "User information not found in session"}), 401
    if not conversation_id:
        return jsonify({"error": "Conversation ID is required"}), 400

    try:
        success = delete_user_conversation(username, email, conversation_id)
        if success:
            logger.info(f"Deleted conversation {conversation_id} for user {username}")
            return jsonify({"status": "success", "message": "Conversation deleted."})
        else:
            # This might happen if the file didn't exist, which is okay for deletion.
            logger.warning(
                f"Attempted to delete non-existent conversation {conversation_id} for user {username}")
            return jsonify(
                {"status": "success", "message": "Conversation not found or already deleted."})
    except Exception as e:
        logger.error(f"Error deleting conversation {conversation_id} for {username}: {e}")
        return jsonify({"error": "Failed to delete conversation"}), 500


@history_bp.route('/api/mark_conversation_as_bad_case', methods=['POST'])
@login_required
def mark_conversation_bad_case_user():
    """
    POST /api/mark_conversation_as_bad_case endpoint that marks a conversation as a bad case.
    """
    user_info = session.get("user_info")
    username = user_info.get("name")
    email = get_valid_user_email(user_info)
    if not username or not email:
        return jsonify({"error": "User information not found in session"}), 401

    data = request.get_json()
    if not data or 'conversation_id' not in data:
        return jsonify({'error': 'Missing conversation_id'}), 400

    conversation_id = data['conversation_id']
    # User marking always implies it's a bad case
    is_bad_case = True

    logger.info(f"User {username} marking conversation {conversation_id} as bad case.")

    try:
        # Call the MySQL version of the function from user_history
        success = mark_bad_case(conversation_id, is_bad_case)
        if success:
            return jsonify({'status': 'success', 'message': 'Conversation marked as bad case.'})
        else:
            # This could happen if the conversation doesn't exist for the user
            logger.warning(
                f"Failed to mark non-existent or unauthorized conversation {conversation_id} as bad case for user {username}")
            return jsonify({
                               'error': 'Failed to mark conversation. It might not exist or belong to this user.'}), 404
    except Exception as e:
        logger.error(
            f"Error marking conversation {conversation_id} as bad case for user {username}: {e}")
        return jsonify({'error': 'Internal server error while marking bad case.'}), 500
