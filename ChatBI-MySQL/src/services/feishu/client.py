import json
import os
import time
import asyncio
import lark_oapi as lark

from src.services.agent.bots.master_controller_bot import MasterControllerBot
from services.agent.utils.model_provider import CUSTOM_MODEL_PROVIDER
from services.chatbot.history_service import (
    save_user_message,
    save_assistant_message,
    get_conversation_history_as_input_list,
    check_conversation_ownership,
)
from src.services.auth.user_login_with_feishu import (
    APP_ID,
    APP_SECRET,
    upsert_user_info,
)
from utils.user_utils import get_api_token
from typing import Optional
from src.utils.logger import logger
from lark_oapi.api.im.v1 import *
from lark_oapi.api.cardkit.v1 import *
from dotenv import load_dotenv

load_dotenv()

# Import agent components
from agents import Runner, RunConfig
from src.models.user_info_class import UserInfo

import requests

# Import message APIs
from src.services.feishu.message_apis import (
    reply_simple_text_message,
    initial_card_message,
    send_updates_to_card,
    send_finished_message_to_card,
    after_badcase_mark,
    lark_client,
    FINAL_LEMENT_ID,
    THINKING_LEMENT_ID,
    FOOTNOTE_ELEMENT_ID,
)

# Import formatter functions
from src.services.feishu.agent_message_formatter import (
    extract_content_from_stream_event,
    THINKING_PROCESS_SPLITTER,
)

HOST_NAME = os.getenv("CHAT_BI_HOST_NAME", "https://chat-bi.summerfarm.net")

MESSAGE_STEPS = int(
    os.getenv("MESSAGE_STEPS", 15)
)  # 每次发送消息到飞书时的步长(字符数)

# 定义为全局变量，用于跟踪已处理的消息ID
message_ids_has_been_processed = {}


def get_feishu_token() -> dict:
    url = "https://open.feishu.cn/open-apis/auth/v3/app_access_token/internal"
    token = requests.post(
        url=url,
        json={"app_id": APP_ID, "app_secret": APP_SECRET},
    ).json()
    logger.info(f"feishu tenant_token:{token}")
    return token


def get_valid_user_email(user_info: dict = {}) -> str:
    email = user_info.get("email")
    if email and "@" in email:
        return email
    else:
        return user_info.get("enterprise_email", "unknown")


from services.auth.user_login_with_feishu import get_user_admin_id
from datetime import datetime


async def handle_agent_query(
    message_id: str, user_query: str, user_info_dict: dict, root_id: str = None
):
    """处理用户查询并将更新流式传输到飞书卡片。"""
    sequence = 0  # Initialize sequence for this message
    card_id = None
    element_id = None

    try:
        user_name = user_info_dict.get("name", "unknown_user")
        user_id = user_info_dict.get("open_id", "unknown_user")
        user_email = get_valid_user_email(user_info_dict)

        logger.info(f"开始处理用户 {user_name} 的查询: {user_query}")

        # 使用root_id作为conversation_id，如果没有则使用message_id
        conversation_id = root_id if root_id else message_id

        # 如果是回复已有对话，检查当前用户是否是对话的所有者
        if root_id and root_id != message_id:
            # 检查对话所有权
            is_owner = check_conversation_ownership(user_name, user_email, root_id)
            if not is_owner:
                logger.warning(
                    f"用户 {user_name} ({user_email}) 尝试回复不属于他的对话 {root_id}"
                )
                reply_simple_text_message(
                    message_id,
                    "抱歉，您不能回复其他人的对话。请开始一个新的对话或回复您自己的对话。",
                )
                return

        # 保存用户消息到历史记录
        save_user_message(
            username=user_name,
            email=user_email,
            conversation_id=conversation_id,
            content=user_query,
            timestamp=int(datetime.now().timestamp() * 1000),
        )
        logger.info(f"用户消息已保存到对话 {conversation_id}")

        # 获取格式化后的历史对话记录
        history = []
        if root_id:
            try:
                # 使用新的 service 函数获取格式化后的历史记录
                history = get_conversation_history_as_input_list(
                    user_name, user_email, conversation_id
                )
                logger.info(
                    f"通过 history_service 获取到 {len(history)} 条格式化后的历史消息。"
                )
            except Exception as e:
                logger.error(
                    f"通过 history_service 获取历史对话记录失败: {e}", exc_info=True
                )
                # 如果获取历史失败，继续处理但不使用历史记录
                history = []
        else:
            logger.info("No root_id provided, starting with empty history.")
            history = []

        # 1. 初始化卡片
        card_id, element_id = initial_card_message(
            message_id,
            "🤖 正在思考中，请稍候...",
            user_query=user_query,
            chat_id=conversation_id,
            user_id=user_id,
        )
        if not card_id:
            logger.error("无法初始化卡片消息，处理中止。")
            reply_simple_text_message(
                message_id, "抱歉，创建回复卡片时出错，请稍后再试。"
            )
            return

        # 2. 初始化Agent
        # 可以在这里添加从环境变量或用户设置中获取model_name的逻辑
        model_name = None  # 默认使用OPENAI_MODEL
        agent, user_info_obj = await initialize_agent_and_user_info(
            user_info_dict, model_name
        )

        # 3. 流式处理Agent响应
        (
            last_sequence,
            full_response,
            full_log_message,
            structured_assistant_message,
        ) = await process_agent_stream(
            agent, user_query, user_info_obj, card_id, element_id, sequence, history
        )

        # 检查是否发生了超时（last_sequence = 0）
        if last_sequence == 0:
            logger.warning("检测到流式响应超时，创建新卡片并发送完整响应")
            # 创建新卡片
            new_card_id, new_element_id = initial_card_message(
                message_id,
                "🕒 响应超时，已重新创建卡片",
                user_query=user_query,
                chat_id=conversation_id,
                user_id=user_id,
            )
            if not new_card_id:
                await asyncio.to_thread(
                    reply_simple_text_message,
                    message_id,
                    f"您的任务：{user_query}\n🤖 响应超时，尝试重新创建卡片，但创建新卡片失败。\n请您去网页版查看结果：{HOST_NAME}?chat={conversation_id}",
                )
                sequence = 0
            else:
                # 更新卡片ID和元素ID
                card_id = new_card_id
                element_id = new_element_id
                # 发送完整响应到新卡片
                sequence = 0  # 重置序列号
                sequence = await asyncio.to_thread(
                    send_updates_to_card,
                    card_id=card_id,
                    markdown_content=full_response,
                    element_id=element_id,
                    sequence=sequence,
                )
        else:
            # 正常情况，使用返回的序列号
            sequence = last_sequence

        # 4. 完成卡片
        if sequence > 0:
            logger.info(
                f"查询处理完成:{full_response}\n最终回复长度: {len(full_response)}.\n即将完成卡片 {card_id}，最终设置序列号 {sequence}。"
            )
            await asyncio.to_thread(
                send_finished_message_to_card,
                card_id=card_id,
                sequence=sequence,
                chat_id=conversation_id,
            )
        else:
            logger.warning(f"由于流式响应超时，跳过完成卡片步骤")

        # 5. 保存助手回复到历史记录
        output_as_input_json = None

        # 尝试序列化结构化消息用于 output_as_input 字段
        if structured_assistant_message and isinstance(
            structured_assistant_message, dict
        ):
            try:
                output_as_input_json = json.dumps(
                    structured_assistant_message, ensure_ascii=False, indent=2
                )
            except Exception as json_err:
                logger.error(
                    f"无法将 structured_assistant_message 序列化为 JSON: {json_err}"
                )
                # 序列化失败，output_as_input_json 保持 None

        # 调用 save_assistant_message，分别传递 stream logs 和 structured output JSON
        save_assistant_message(
            username=user_name,
            email=user_email,
            conversation_id=conversation_id,
            content=full_response,  # content 始终是流式文本响应
            timestamp=int(datetime.now().timestamp() * 1000),
            logs=full_log_message,  # 始终传递原始流日志到 logs 字段
            output_as_input=output_as_input_json,  # 传递序列化后的结构化输出（或 None）到新字段
        )
        # 更新日志消息以反映保存状态
        logger.info(
            f"助手回复已保存到对话 {conversation_id} (结构化输出状态: {'已保存' if output_as_input_json else '未保存或失败'})"
        )
    except Exception as e:
        await handle_agent_query_error(
            e,
            message_id,
            card_id,
            element_id,
            sequence,  # Pass the current sequence number
        )


async def initialize_agent_and_user_info(user_info_dict: dict, model_name: str = None):
    """初始化Agent和用户信息对象

    Args:
        user_info_dict: 用户信息字典
        model_name: 可选的模型名称，默认为None（使用OPENAI_MODEL）
    """
    if "admin_id" not in user_info_dict:
        name = user_info_dict.get("name")
        email = get_valid_user_email(user_info_dict)
        user_info_dict["admin_id"] = get_user_admin_id(email=email, user_name=name)

    # 创建MasterControllerBot实例
    bot = MasterControllerBot(user_info_dict)

    # 使用可选的model_name创建agent
    agent = bot.create_agent(model_name)

    # 创建UserInfo对象时只传入必要的字段
    user_info_obj = UserInfo(
        user_name=user_info_dict.get("name"),
        email=get_valid_user_email(user_info_dict),
        access_token=get_feishu_token().get("tenant_access_token"),
        union_id=user_info_dict.get("union_id"),
        summerfarm_api_token=user_info_dict.get("summerfarm_api_token")
    )

    return agent, user_info_obj


async def process_agent_stream(
    agent,
    user_query,
    user_info_obj,
    card_id,
    element_id,
    initial_sequence: int,
    history=None,
) -> tuple[int, str, str, dict | None]:  # 更新返回类型
    """处理Agent的流式响应并更新卡片, 返回 (last_sequence, full_response, full_log_message, structured_assistant_message)"""
    current_sequence = initial_sequence  # Sequence for main content updates
    full_response = ""
    full_log_message = ""
    last_sent_length = 0  # 记录上一次发送的内容长度
    accumulated_content = ""  # 累积的内容
    retry_count = 0  # 重试计数器
    max_retries = 2  # 最大重试次数
    retry_interval = 2  # 重试间隔（秒）

    # 记录流式响应的开始时间
    start_time = time.time()
    # 从环境变量获取超时时间（分钟），默认为9分钟
    timeout_minutes = float(os.getenv("STREAM_TIMEOUT_MINUTES", 9))
    logger.info(f"流式响应开始时间: {start_time}, 超时时间设置: {timeout_minutes}分钟")

    # 准备输入，包括历史记录（如果有）
    input_messages = []
    if history and isinstance(history, list):
        # 添加历史消息，但排除最后一条用户消息（因为我们会单独添加当前查询）
        for msg in history:
            if msg["role"] == "user" and msg["content"] == user_query:
                continue
            input_messages.append(msg)
        logger.info(f"添加了 {len(input_messages)} 条历史消息到AI输入")

    # 添加当前用户查询
    input_messages.append({"role": "user", "content": user_query})

    while True:  # 添加循环以支持重试
        result = Runner.run_streamed(
            agent,
            input=input_messages,
            run_config=RunConfig(model_provider=CUSTOM_MODEL_PROVIDER),
            context=user_info_obj,
        )

        # 重置响应变量（如果是重试）
        if retry_count > 0:
            full_response = ""
            full_log_message = ""
            last_sent_length = 0
            accumulated_content = ""
            logger.info(f"重试次数: {retry_count}")

        async for item in result.stream_events():
            content_chunk, log_message = extract_content_from_stream_event(item)

            # 记录调试信息
            if log_message:
                logger.debug(f"Stream Event ({item.type}): {log_message}")
                full_log_message += log_message

            # 更新卡片内容
            if content_chunk:
                full_response += content_chunk
                accumulated_content += content_chunk

                # 检查累积内容是否比上次发送的内容长度超过MESSAGE_STEPS个字符
                if len(accumulated_content) > MESSAGE_STEPS:
                    # 检查是否已经超时
                    elapsed_minutes = (time.time() - start_time) / 60
                    if elapsed_minutes >= timeout_minutes:
                        logger.warning(
                            f"流式响应已超时 ({elapsed_minutes:.2f}分钟 >= {timeout_minutes}分钟)，停止发送更新"
                        )
                        current_sequence = 0  # 将序列号设为0，表示发生了超时异常
                        continue

                    current_sequence += 1
                    # 发送累积的响应到卡片 (main content)
                    final_thinking_markdown = full_response
                    final_reply_markdown = None
                    if THINKING_PROCESS_SPLITTER in full_response:
                        final_reply_markdown = full_response.split(
                            THINKING_PROCESS_SPLITTER
                        )[-1]
                        final_thinking_markdown = full_response.replace(
                            final_reply_markdown, ""
                        )

                    if (
                        not final_reply_markdown
                        or len(final_reply_markdown) < MESSAGE_STEPS
                    ):
                        # 如果final_reply_markdown内容小于MESSAGE_STEPS个字符，才需要发送思考过程，节约资源
                        current_sequence = await asyncio.to_thread(
                            send_updates_to_card,
                            card_id=card_id,
                            markdown_content=final_thinking_markdown,
                            element_id=THINKING_LEMENT_ID,
                            sequence=current_sequence,
                        )
                    if final_reply_markdown:
                        current_sequence = await asyncio.to_thread(
                            send_updates_to_card,
                            card_id=card_id,
                            markdown_content=final_reply_markdown,
                            element_id=FINAL_LEMENT_ID,
                            sequence=current_sequence,
                        )
                    last_sent_length = len(full_response)
                    accumulated_content = ""  # 重置累积内容

        # 流式响应结束后，确保发送完整的最终响应
        if current_sequence == 0:
            logger.warning(f"流式响应超时，未发送最终主内容更新\n:{full_response}")
        elif full_response and (len(full_response) > last_sent_length):
            logger.info(
                f"发送最终主内容更新 {current_sequence} 到卡片 {card_id} (总内容长度: {len(full_response)})"
            )
            final_thinking_markdown = full_response
            final_reply_markdown = None
            if THINKING_PROCESS_SPLITTER in full_response:
                final_reply_markdown = full_response.split(THINKING_PROCESS_SPLITTER)[
                    -1
                ]
                final_thinking_markdown = full_response.replace(
                    final_reply_markdown, ""
                )

            current_sequence = await asyncio.to_thread(
                send_updates_to_card,
                card_id=card_id,
                markdown_content=final_thinking_markdown,
                element_id=THINKING_LEMENT_ID,
                sequence=current_sequence,
            )
            if final_reply_markdown:
                current_sequence = await asyncio.to_thread(
                    send_updates_to_card,
                    card_id=card_id,
                    markdown_content=final_reply_markdown,
                    element_id=FINAL_LEMENT_ID,
                    sequence=current_sequence,
                )

        # 检查是否包含Handoff日志，如果不包含且未达到最大重试次数，则重试
        if "Handoff from " not in full_log_message and retry_count < max_retries:
            logger.warning(
                f"未检测到Handoff日志，准备重试 (重试 {retry_count + 1}/{max_retries})"
            )
            retry_count += 1

            # 更新卡片，告知用户正在重试
            current_sequence += 1
            retry_message = "🤖发生了一点小故障，重试中..."
            current_sequence = await asyncio.to_thread(
                send_updates_to_card,
                card_id=card_id,
                markdown_content=retry_message,
                element_id=THINKING_LEMENT_ID,
                sequence=current_sequence,
            )

            # 继续循环，重新运行AI
            await asyncio.sleep(
                retry_interval
            )  # 在异步函数中使用 asyncio.sleep 避免阻塞事件循环
            continue
        else:
            # 如果包含Handoff日志或已达到最大重试次数，则跳出循环
            if retry_count > 0:
                logger.info(
                    f"重试完成，{'检测到Handoff日志' if 'Handoff from ' in full_log_message else '达到最大重试次数'}"
                )
            break

    # 尝试提取结构化的助手消息
    structured_assistant_message = None
    try:
        input_list = result.to_input_list()
        # 检查列表是否非空，最后一个元素是否为字典，且角色是否为 'assistant'
        if (
            input_list
            and isinstance(input_list[-1], dict)
            and input_list[-1].get("role") == "assistant"
        ):
            structured_assistant_message = input_list[-1]
            logger.info(
                f"提取到的结构化助手消息: {str(structured_assistant_message)[:200]}..."
            )  # 记录前200字符
        else:
            logger.warning(f"无法从 to_input_list() 提取有效的助手消息: {input_list}")
    except Exception as e:
        logger.error(f"提取 to_input_list() 时出错: {e}", exc_info=True)

    return (
        current_sequence,
        full_response,
        full_log_message,
        structured_assistant_message,  # 返回提取到的结构化消息
    )


async def handle_agent_query_error(
    error, message_id, card_id=None, element_id=None, sequence=0
):
    """处理Agent查询过程中的错误"""
    logger.error(f"处理Agent查询时出错: {error}", exc_info=True)

    # 尝试发送错误消息到卡片，如果可能的话，否则回复文本
    try:
        error_message = f"处理您的请求时发生内部错误。\n错误信息: {error}"
        if card_id:
            error_sequence = sequence + 1
            await asyncio.to_thread(
                send_updates_to_card,
                card_id,
                error_message,
                element_id,  # Should be "markdown_1"
                sequence=error_sequence,  # Use incremented sequence for error message
            )
            finish_sequence = error_sequence + 1
            await asyncio.to_thread(
                send_finished_message_to_card,
                card_id,
                sequence=finish_sequence,
                chat_id="invalid_chat_id",
            )
        else:
            reply_simple_text_message(message_id, error_message)
    except Exception as send_error:
        logger.error(f"发送错误消息到飞书时再次出错: {send_error}")
        reply_simple_text_message(
            message_id, "处理您的请求时发生内部错误，并且无法更新卡片状态。"
        )


# 处理卡片按钮点击回调
# https://open.feishu.cn/document/uAjLw4CM/ukzMukzMukzM/feishu-cards/card-callback-communication
from lark_oapi.event.callback.model.p2_card_action_trigger import (
    P2CardActionTrigger,
    P2CardActionTriggerResponse,
)
from src.services.chatbot.bad_case_service import mark_bad_case


def _async_do_card_action_trigger(
    data: P2CardActionTrigger,
) -> P2CardActionTriggerResponse:
    logger.info(lark.JSON.marshal(data))
    conversation_id = data.event.action.value.get("conversation_id")
    card_id = data.event.action.value.get("card_id")
    logger.info(f"用户准备将 conversation_id 标记为 badcase:{conversation_id}") # 用户准备将 conversation_id 标记为 badcase
    if conversation_id:
        mark_bad_case(conversation_id=conversation_id, is_bad_case=True)

        # 定义一个异步辅助函数来处理延迟和调用 after_badcase_mark
        async def _delayed_after_badcase_mark_task(current_card_id: str):
            await asyncio.sleep(1.5) # 异步等待1.5秒
            # after_badcase_mark 是一个阻塞I/O操作（网络请求）
            # 应在线程中运行以避免阻塞 asyncio 事件循环
            await asyncio.to_thread(after_badcase_mark, current_card_id)

        # 创建一个后台任务来执行延迟调用
        # 这允许 _async_do_card_action_trigger 立即返回响应
        # 此处假设该同步函数是在一个已正确设置 asyncio 事件循环的上下文中被调用的
        if card_id: # 仅在 card_id 有效时创建任务
            asyncio.create_task(_delayed_after_badcase_mark_task(card_id))
        else:
            logger.warning("card_id 为空，无法调度 after_badcase_mark 的延迟调用")

    resp = {
        "toast": {
            "type": "info",
            "content": "已标记为Badcase，感谢反馈！",
        },
    }
    return P2CardActionTriggerResponse(resp)


async def _async_do_p2_im_message_receive_v1(
    data: lark.im.v1.P2ImMessageReceiveV1,
) -> None:
    """处理接收到的飞书消息 (Actual Async Logic)"""
    message_data_str = lark.JSON.marshal(
        data
    )  # Keep original data marshaling if needed elsewhere
    logger.info(f"Async handler processing message: {message_data_str}")

    if not data.event.message.content:
        logger.info(f"无需处理: 消息内容为空")
        return

    message_id = data.event.message.message_id
    root_id = data.event.message.root_id or message_id
    # 获取chat_id但目前未使用，保留以备将来使用，目前已使用root_id
    # 使用全局变量，确保在函数内部正确引用
    global message_ids_has_been_processed
    # Simple in-memory processed check (consider a more robust solution for distributed systems)
    if message_id in message_ids_has_been_processed:
        logger.info(
            f"消息:{message_id}已处理:{message_ids_has_been_processed[message_id]}"
        )
        return
    else:
        message_ids_has_been_processed[message_id] = time.time()
        # Clean up old entries periodically if memory usage is a concern
        if len(message_ids_has_been_processed) > 1000:  # Example cleanup threshold
            cutoff = time.time() - 3600  # Keep last hour
            message_ids_has_been_processed = {
                k: v for k, v in message_ids_has_been_processed.items() if v > cutoff
            }
        logger.info(f"即将处理消息:{message_id}")

    # Check sender type, ignore bot messages if necessary
    if data.event.sender.sender_type != "user":
        logger.info(f"忽略非用户消息: sender_type={data.event.sender.sender_type}")
        return

    user_open_id = data.event.sender.sender_id.open_id
    if not user_open_id:
        logger.info(f"无需处理: 无法获取 user_open_id")
        return

    user_info_str = get_user_info(user_id=user_open_id)
    if not user_info_str:
        logger.info(f"获取用户失败: user_open_id:{user_open_id}")
        reply_simple_text_message(
            message_id, "抱歉，无法获取您的用户信息，请稍后再试。"
        )
        return

    user_info_dict = json.loads(
        user_info_str
    )  # Parse the JSON string returned by get_user_info
    logger.info(f"获取用户成功:{user_info_dict}")
    upsert_user_info(
        name=user_info_dict.get("name", ""),
        email=user_info_dict.get("email", ""),
        user_id=user_info_dict.get("user_id", ""),
        job_title=user_info_dict.get("job_title", ""),
        open_id=user_open_id,
    )
    
    # 追加union_id、summerfarm_api_token
    union_id = user_info_dict["union_id"]
    api_token = get_api_token(union_id=union_id)
    logger.info(f"获取到了 api_token:{api_token}")
    user_info_dict["summerfarm_api_token"] = api_token
    
    # 解析消息内容
    content_data = json.loads(data.event.message.content)

    # 检查是否包含 @_all
    if check_content_has_at_all(content_data):
        logger.info(f"跳过处理: 消息包含 @_all：{content_data}")
        return

    # 提取文本内容
    user_query = extract_text_from_content(content_data)

    if not user_query:
        logger.info(f"无需处理: 消息文本为空")
        reply_simple_text_message(message_id, "请输入您的问题。")
        return

    if "@_user_1" in user_query:
        # Remove the mention and strip whitespace
        user_query = user_query.replace("@_user_1", "").strip()

    if len(user_query.strip()) <= 5 and (root_id == message_id):
        # 只有新对话的时候，才去校验输入的合法性
        logger.info(f"用户消息过短: {user_query}")  # 添加日志记录
        # 定义回复用户的文本内容
        reply_text = """您的问题太短了，您可尝试这样提问：
1. 我的客户中，过去30天购买了安佳淡奶油的有哪些？列出他们的详细信息。
2. 杭州市过去7天新增了多少新注册的门店？
3. 我的客户中，有哪些客户在过去6个月曾经购买过10单以上，但是最近30天都未再来下单的？列出他们的详细信息（流失用户分析）。
4. 杭州市过去30天下单数最大的客户是那几个？
5. 门店ID=15062 的商户下单量最大的商品是哪些？列出他过去30天的下单商品详细信息
6. 安佳淡奶油在全国各个仓库的库存情况是怎样的？"""
        reply_simple_text_message(message_id, reply_text)
        return

    # Directly handle the message with the agent
    try:
        # Create a task to handle the agent processing asynchronously
        # Pass the parsed user_info_dict and root_id for conversation history
        asyncio.create_task(
            handle_agent_query(message_id, user_query.strip(), user_info_dict, root_id)
        )
    except Exception as e:
        logger.error(f"启动Agent任务时出错: {e}")
        reply_simple_text_message(message_id, f"处理您的请求时遇到错误: {e}")


def sync_wrapper_do_p2_im_message_receive_v1(
    data: lark.im.v1.P2ImMessageReceiveV1,
) -> None:
    """
    同步包装器，用于调度异步消息处理器。
    这个函数本身不会启动新的操作系统线程。
    它使用 asyncio.create_task 将 _async_do_p2_im_message_receive_v1 协程作为一个任务调度到事件循环中。
    这个任务将在运行事件循环的同一个线程内并发执行。因此，每次调用此函数会创建一个异步任务，但不会创建新的线程。
    """
    try:
        logger.info(f"同步包装器收到消息: {data.event.message.message_id}")
        # 将异步处理程序调度到事件循环上运行
        # 注意：asyncio.create_task 不会创建新线程，而是在当前事件循环线程中创建并调度一个并发任务 (Task)。
        asyncio.create_task(_async_do_p2_im_message_receive_v1(data))
    except Exception as e:
        logger.error(f"调度异步消息处理任务时出错: {e}", exc_info=True)


# Updated event handler registration
event_handler = (
    lark.EventDispatcherHandler.builder("", "")
    .register_p2_im_message_receive_v1(sync_wrapper_do_p2_im_message_receive_v1)
    .register_p2_card_action_trigger(_async_do_card_action_trigger)
    .build()
)


def start_feishu_client():
    """启动飞书客户端WebSocket连接"""
    logger.info(f"启动飞书客户端WebSocket连接:{APP_ID}")
    cli = lark.ws.Client(
        APP_ID, APP_SECRET, event_handler=event_handler, log_level=lark.LogLevel.INFO
    )
    cli.start()


from lark_oapi.api.contact.v3 import *
from lark_oapi.api.contact.v3.model import *


def get_user_info(user_id: str) -> None:
    """使用tenant token获取用户信息"""

    request: GetUserRequest = (
        GetUserRequest.builder()
        .user_id_type("open_id")
        .user_id(user_id)
        .department_id_type("open_department_id")
        .build()
    )

    response: GetUserResponse = lark_client.contact.v3.user.get(request)

    if not response.success():
        lark.logger.error(
            f"获取用户失败, user_id:{user_id}, reponse: {response.raw.content}"
        )
        return None

    return lark.JSON.marshal(response.data.user)


def check_content_has_at_all(content_data: dict) -> bool:
    """检查消息内容中是否包含 @_all

    Args:
        content_data: 解析后的消息内容字典

    Returns:
        bool: 如果消息中包含 @_all 则返回 True，否则返回 False
    """
    # 处理纯文本消息
    if "text" in content_data:
        return "@_all" in content_data.get("text", "")

    # 处理富文本消息
    if "content" in content_data:
        content_array = content_data.get("content", [])
        for content_block in content_array:
            for element in content_block:
                if element.get("tag") == "at" and element.get("user_id") == "@_all":
                    return True
    return False


def extract_text_from_content(content_data: dict) -> str:
    """从消息内容中提取文本

    Args:
        content_data: 解析后的消息内容字典

    Returns:
        str: 提取出的文本内容
    """
    # 处理纯文本消息
    if "text" in content_data:
        return content_data.get("text", "")

    # 处理富文本消息
    text_content = ""
    if "content" in content_data:
        content_array = content_data.get("content", [])
        for content_block in content_array:
            for element in content_block:
                if element.get("tag") == "text":
                    text_content += element.get("text", "")
    return text_content


if __name__ == "__main__":
    start_feishu_client()
