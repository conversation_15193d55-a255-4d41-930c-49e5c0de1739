import json
import uuid
import os
from datetime import datetime
import lark_oapi as lark
from lark_oapi.api.im.v1 import *
from lark_oapi.api.cardkit.v1 import *
from src.utils.logger import logger
from src.services.auth.user_login_with_feishu import APP_ID, APP_SECRET
from dotenv import load_dotenv
from .agent_message_formatter import THINKING_PROCESS_SPLITTER

load_dotenv()

THINKING_LEMENT_ID = "markdown_thinking"
FINAL_LEMENT_ID = "markdown_final_reply"
FOOTNOTE_ELEMENT_ID = "markdown_footnote"
FEEDBACK_ELEMENT_ID = "badcase_feedback"

# 获取Bad Case通知群聊ID
BAD_CASE_NOTIFICATION_CHAT_ID = os.getenv("BAD_CASE_NOTIFICATION_CHAT_ID")

# Create lark client
lark_client = (
    lark.Client.builder()
    .app_id(APP_ID)
    .app_secret(APP_SECRET)
    .timeout(3)
    .log_level(lark.LogLevel.INFO)
    .build()
)

LOG_SERVER_NAME = bool(os.getenv("LOG_SERVER_NAME", False))
# Get server name from command `hostname`
SERVER_NAME = os.popen("hostname").read().strip()


def reply_simple_text_message(message_id: str, text: str):
    """回复简单文本消息"""
    # 正确创建请求对象
    req: ReplyMessageRequest = (
        ReplyMessageRequest.builder()
        .message_id(message_id)
        .request_body(
            ReplyMessageRequestBody.builder()
            .content(json.dumps({"text": text}))
            .msg_type("text")
            .reply_in_thread(False)
            .uuid(str(uuid.uuid4()))
            .build()
        )
        .build()
    )

    logger.info(f"reply_simple_text_message to {message_id}: {text}")
    # 发送请求
    response: ReplyMessageResponse = lark_client.im.v1.message.reply(request=req)
    logger.info(f"reply_simple_text_message response: {response.raw.content}")


def get_update_footnote(chat_id: str) -> str:  # 确保 chat_id 参数存在
    # 更新脚注内容以包含指向网页版的链接，使用查询参数而不是路径参数
    HOST_NAME = os.getenv("CHAT_BI_HOST_NAME", "https://chat-bi.summerfarm.net")
    return f"> [生成中...] 最后更新于:{datetime.now().strftime('%m-%d %H:%M:%S')}\n如果长时间未更新，[请点击此处在网页版查看结果]({HOST_NAME}/?chat={chat_id})"


def get_markdown_post_object(
    markdown_content: str,
    title: str = "来自ChatBI的回复",
    chat_id: str = None,
    user_id: str = None,
) -> str:
    """获取Markdown消息对象
    https://open.feishu.cn/document/feishu-cards/card-json-v2-structure
    """
    footnote = (
        get_update_footnote(chat_id)
        if chat_id
        else f"> [生成中...] 最后更新于:{datetime.now().strftime('%m-%d %H:%M:%S')}"
    )
    if LOG_SERVER_NAME:
        footnote += f"\n> [服务器: {SERVER_NAME}]"

    content_json = {
        "schema": "2.0",
        "config": {
            "wide_screen_mode": True,
            "streaming_mode": True,
            "streaming_config": {
                "print_frequency_ms": {
                    "default": 50,
                },
                "print_step": {"default": 5},
                "print_strategy": "fast",
            },
            "width_mode": "fill",
            "enable_forward": True,
        },
        "body": {
            "elements": [
                {
                    "tag": "collapsible_panel",
                    "expanded": False,
                    "header": {
                        "title": {
                            "tag": "plain_text",
                            "content": "🤖思考过程(可点击展开)",
                        },
                        "vertical_align": "center",
                        "icon": {
                            "tag": "standard_icon",
                            "token": "down-small-ccm_outlined",
                            "color": "",
                            "size": "16px 16px",
                        },
                        "icon_position": "right",
                        "icon_expanded_angle": -180,
                    },
                    "border": {"color": "grey", "corner_radius": "5px"},
                    "vertical_spacing": "8px",
                    "padding": "8px 8px 8px 8px",
                    "elements": [
                        {
                            "tag": "markdown",
                            "content": markdown_content,
                            "element_id": THINKING_LEMENT_ID,
                        }
                    ],
                },
                {
                    "tag": "markdown",
                    "content": THINKING_PROCESS_SPLITTER,
                },
                {
                    "tag": "markdown",
                    "content": "☕️ 请稍等...",
                    "element_id": FINAL_LEMENT_ID,
                },
                {
                    "tag": "button",
                    "element_id": FEEDBACK_ELEMENT_ID,
                    "type": "primary",
                    "size": "small",
                    "text": {"tag": "plain_text", "content": "数据不准👎"},
                    "disabled": True,
                    "disabled_tips": {},
                    "behaviors": [
                        {"type": "callback", "value": {"conversation_id": f"{chat_id}"}}
                    ],
                },
                {
                    "tag": "markdown",
                    "content": footnote,
                    "element_id": FOOTNOTE_ELEMENT_ID,
                },
            ]
        },
        "header": {
            "template": "blue",
            "title": {"content": f"<at id={user_id}></at> {title}", "tag": "lark_md"},
        },
    }
    return json.dumps(content_json, ensure_ascii=False)


def initial_card_message(
    message_id: str,
    card_content: str = "您的消息已收到，正在处理...",
    user_query: str = None,
    chat_id: str = None,
    user_id: str = None,
) -> tuple[str, str]:
    """初始化卡片消息
    返回(card_id, element_id) 的组合"""

    # 构造创建Card请求对象
    request: CreateCardRequest = (
        CreateCardRequest.builder()
        .request_body(
            CreateCardRequestBody.builder()
            .type("card_json")
            .data(
                get_markdown_post_object(
                    card_content,
                    title=f"ChatBI回复: {user_query}",
                    chat_id=chat_id,
                    user_id=user_id,
                )
            )
            .build()
        )
        .build()
    )

    # 发起创建Card请求
    logger.info(
        f"Creating card for message {message_id} with content: {lark.JSON.marshal(request.body)}"
    )
    response: CreateCardResponse = lark_client.cardkit.v1.card.create(request)
    if not response.success():
        logger.error(f"Failed to create card: {response.raw.content}")
        return None, None
    card_id = response.data.card_id
    logger.info(f"Card created successfully: {card_id}")

    # 构造消息请求对象
    request: ReplyMessageRequest = (
        ReplyMessageRequest.builder()
        .message_id(message_id)
        .request_body(
            ReplyMessageRequestBody.builder()
            .content(json.dumps({"type": "card", "data": {"card_id": card_id}}))
            .msg_type("interactive")
            .reply_in_thread(False)
            .uuid(str(uuid.uuid4()))
            .build()
        )
        .build()
    )

    # 发送消息的请求
    response: ReplyMessageResponse = lark_client.im.v1.message.reply(request)

    # 处理失败返回
    if not response.success():
        lark.logger.error(f"send card failed, code resp: {response.raw.content}")
        return None, None  # Return None if failed

    # 处理业务结果
    logger.info(f"send card success, code resp: {lark.JSON.marshal(response.data)}")

    return (
        card_id,
        THINKING_LEMENT_ID,
    )


def send_updates_to_card(
    card_id: str,
    markdown_content: str,
    element_id: str,  # 主内容（通常是思考过程）的元素ID
    sequence: int = 0,  # 初始序列号
) -> int:
    if not markdown_content:
        return sequence
    # 移除markdown_content前面的多余换行符
    markdown_content = markdown_content.lstrip("\n")

    # 构造最终答案更新请求
    final_answer_request: ContentCardElementRequest = (
        ContentCardElementRequest.builder()
        .card_id(card_id)
        .element_id(element_id)
        .request_body(
            ContentCardElementRequestBody.builder()
            .uuid(str(uuid.uuid4()))
            .content(markdown_content)
            .sequence(sequence)
            .build()
        )
        .build()
    )
    response: ContentCardElementResponse = lark_client.cardkit.v1.card_element.content(
        final_answer_request
    )
    if response.success():
        logger.info(
            f"Update card element (seq: {sequence}) to {element_id} successful. Response: {response.raw.content}"
        )

    return sequence + 1  # 返回最终的序列号


def after_badcase_mark(card_id: str) -> int:
    if not card_id:
        logger.error("card_id is empty")
        return 0
    post_json = {
        "disabled": True,
        "text": {"content": "您已标记为数据不准案例，感谢您的反馈！"},
    }
    request: PatchCardElementRequest = (
        PatchCardElementRequest.builder()
        .card_id(card_id)
        .element_id(FEEDBACK_ELEMENT_ID)
        .request_body(
            PatchCardElementRequestBody.builder()
            .partial_element(json.dumps(post_json, ensure_ascii=False))
            .uuid(str(uuid.uuid4()))
            .sequence(5000)  # 使用非常大的序列号，确保覆盖之前的所有操作
            .build()
        )
        .build()
    )
    response: ContentCardElementResponse = lark_client.cardkit.v1.card_element.patch(
        request
    )
    logger.info(f"response: {response.raw.content}")
    return response.code


def send_finished_message_to_card(card_id: str, chat_id: str, sequence: int) -> int:
    """更新脚注到最终状态，然后发送最终设置更新"""
    final_footnote_content = f"> 任务完成于:{datetime.now().strftime('%m-%d %H:%M:%S')}"
    logger.info(final_footnote_content)

    # 使用同步方式更新脚注
    request: ContentCardElementRequest = (
        ContentCardElementRequest.builder()
        .card_id(card_id)
        .element_id(FOOTNOTE_ELEMENT_ID)
        .request_body(
            ContentCardElementRequestBody.builder()
            .uuid(str(uuid.uuid4()))
            .content(final_footnote_content)
            .sequence(sequence)
            .build()
        )
        .build()
    )
    response: ContentCardElementResponse = lark_client.cardkit.v1.card_element.content(
        request
    )
    logger.info(
        f"Footnote update response: {response.raw.content}, sequence: {sequence}"
    )
    sequence += 1

    update_card_patch = {
        "disabled": False,
        "behaviors": [
            {
                "type": "callback",
                "value": {"conversation_id": f"{chat_id}", "card_id": card_id},
            }
        ],
    }
    request: PatchCardElementRequest = (
        PatchCardElementRequest.builder()
        .card_id(card_id)
        .element_id(FEEDBACK_ELEMENT_ID)
        .request_body(
            PatchCardElementRequestBody.builder()
            .partial_element(json.dumps(update_card_patch, ensure_ascii=False))
            .uuid(str(uuid.uuid4()))
            .sequence(sequence)
            .build()
        )
        .build()
    )
    response: ContentCardElementResponse = lark_client.cardkit.v1.card_element.patch(
        request
    )
    logger.info(
        f"Update feedback element response: {response.raw.content}, sequence: {sequence}"
    )
    sequence += 1

    logger.info(
        f"Sending final settings update for card: {card_id}, sequence: {sequence}"
    )
    json_str = json.dumps(
        {
            "config": {
                "enable_forward": True,
                "enable_forward_interaction": False,
                "streaming_mode": False,
                "update_multi": True,
                "width_mode": "fill",
            }
        }
    )
    # 构造请求对象
    request: SettingsCardRequest = (
        SettingsCardRequest.builder()
        .card_id(card_id)
        .request_body(
            SettingsCardRequestBody.builder()
            .settings(json_str)
            .uuid(str(uuid.uuid4()))
            .sequence(sequence)
            .build()
        )
        .build()
    )

    # 发起请求
    response: SettingsCardResponse = lark_client.cardkit.v1.card.settings(request)
    logger.info(f"settings card response: {response.raw.content}")
    return sequence + 1


def send_message_to_chat(chat_id: str, message: str, msg_type: str = "text") -> bool:
    """
    发送消息到指定的飞书群聊

    Args:
        chat_id (str): 群聊ID
        message (str): 要发送的消息内容
        msg_type (str): 消息类型，默认为"text"

    Returns:
        bool: 发送成功返回True，失败返回False
    """
    if not chat_id:
        logger.warning("群聊ID为空，无法发送消息")
        return False

    logger.info(f"准备发送消息到群聊 {chat_id}: {message}")


    try:
        # 构造发送消息请求
        request: CreateMessageRequest = (
            CreateMessageRequest.builder()
            .receive_id_type("chat_id")
            .request_body(
                CreateMessageRequestBody.builder()
                .receive_id(chat_id)
                .content(json.dumps({"text": message}))
                .msg_type(msg_type)
                .uuid(str(uuid.uuid4()))
                .build()
            )
            .build()
        )

        # 发送消息
        response: CreateMessageResponse = lark_client.im.v1.message.create(request)

        if response.success():
            logger.info(f"成功发送消息到群聊 {chat_id}: {message}")
            return True
        else:
            logger.error(f"发送消息到群聊失败: {response.raw.content}")
            return False

    except Exception as e:
        logger.error(f"发送消息到群聊时发生异常: {str(e)}", exc_info=True)
        return False


def send_bad_case_notification(conversation_id: str, user_name: str = None) -> bool:
    """
    发送Bad Case标记通知到指定群聊

    Args:
        conversation_id (str): 对话ID
        user_name (str, optional): 用户名称

    Returns:
        bool: 发送成功返回True，失败返回False
    """
    if not BAD_CASE_NOTIFICATION_CHAT_ID:
        logger.warning("未配置Bad Case通知群聊ID，跳过发送通知")
        return False

    # 构造通知消息
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    user_info = f"**用户**: {user_name}" if user_name else "**用户**: 未知用户"

    # 获取主机名用于构造Dashboard链接
    HOST_NAME = os.getenv("CHAT_BI_HOST_NAME", "https://chat-bi.summerfarm.net")
    dashboard_link = f"{HOST_NAME}/dashboard?chat={conversation_id}"

    message = f"""## 🚨 Bad Case 标记通知

{user_info}
**对话ID**: `{conversation_id}`
**标记时间**: {timestamp}

**操作**: [📊 查看对话详情]({dashboard_link})

---
请相关人员及时关注和处理。"""

    return send_message_to_chat(BAD_CASE_NOTIFICATION_CHAT_ID, message, msg_type="text")
