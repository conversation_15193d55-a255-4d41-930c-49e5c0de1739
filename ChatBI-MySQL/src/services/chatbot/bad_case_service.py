"""
Chat quality service module.

This module provides business logic for managing chatbot quality and bad cases.
"""

from typing import Optional

from src.utils.logger import logger
from src.repositories.chatbi.bad_case import (
    mark_conversation_as_bad_case,
    get_conversation_bad_case_status,
)

def mark_bad_case(conversation_id: str, is_bad_case: bool = True) -> bool:
    """
    Mark or unmark a conversation as a bad case.

    Args:
        conversation_id (str): The ID of the conversation
        is_bad_case (bool, optional): Whether to mark as bad case (True) or unmark (False). Defaults to True.

    Returns:
        bool: True if the operation was successful, False otherwise
    """
    action = "Marking" if is_bad_case else "Unmarking"
    logger.info(f"{action} conversation {conversation_id} as bad case")
    return mark_conversation_as_bad_case(conversation_id, is_bad_case)

def is_bad_case(conversation_id: str, username: Optional[str] = None, email: Optional[str] = None) -> bool:
    """
    Check if a conversation is marked as a bad case.

    Args:
        conversation_id (str): The ID of the conversation
        username (str, optional): The username to filter by. Defaults to None.
        email (str, optional): The email to filter by. Defaults to None.

    Returns:
        bool: True if the conversation is marked as a bad case, False otherwise
    """
    logger.info(f"Checking if conversation {conversation_id} is a bad case")
    return get_conversation_bad_case_status(conversation_id, username, email)
