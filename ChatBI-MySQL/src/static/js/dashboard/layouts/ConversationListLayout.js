/**
 * ConversationListLayout Component
 *
 * Dashboard conversation list section with filters and table
 * Follows Apple/OpenAI-inspired aesthetic with clean design
 * Uses DaisyUI components for consistent styling with proper dark mode support
 */
import {ref, reactive, computed, onMounted, watch} from 'vue';
import GroupSection from '../components/GroupSection.js';
import ConversationTableCard from '../components/cards/ConversationTableCard.js';
import ConversationFilters from '../components/ConversationFilters.js';
import { fetchConversations } from '../services/conversationService.js';

export default {
    name: 'ConversationListLayout',
    components: {
        GroupSection,
        ConversationTableCard,
        ConversationFilters
    },
    setup() {
        // Responsive behavior
        const isDesktopView = computed(() => {
            return window.innerWidth >= 1024; // lg breakpoint
        });

        // Handle page change from ConversationTable
        const handlePageChange = (page) => {
            currentPage.value = page;
            loadConversations();
        };
        // Loading state
        const isLoading = ref(false);

        // Date range state
        const startDate = ref('');
        const endDate = ref('');

        // Filter inputs
        const filters = reactive({
                                     username: '',
                                     conversation_id: '',
                                     content_search: '',
                                     bad_case_filter: 'all' // 'all', 'only_bad', 'only_good'
                                 });

        // Pagination
        const currentPage = ref(1);
        const totalCount = ref(0);
        const pageSize = ref(10);

        // Conversations data
        const conversations = ref({});

        // Error state
        const error = ref(null);

        // Computed property for formatted conversations
        const formattedConversations = computed(() => {
            const result = [];
            Object.keys(conversations.value).forEach(id => {
                const convo = conversations.value[id];
                // 保留原始对话的所有属性，同时添加一些格式化的属性
                result.push({
                    // 原始属性
                    conversation_id: convo.conversation_id,
                    id: convo.conversation_id,
                    title: convo.title || '无标题对话',
                    username: convo.username || '未知用户',
                    email: convo.email || '',
                    is_bad_case: convo.is_bad_case,
                    messages: convo.messages || [],

                    // 格式化的属性
                    lastMessageTime: new Date(convo.last_message_time).toLocaleString(),
                    timestamp: convo.last_message_time,
                    isBadCase: convo.is_bad_case,
                    messageCount: convo.messages ? convo.messages.length : 0,
                });
            });
            // Sort by timestamp (newest first)
            return result.sort((a, b) => b.timestamp - a.timestamp);
        });

        // Load conversations from API
        const loadConversations = async () => {
            isLoading.value = true;
            error.value = null;

            try {
                // Calculate offset based on current page and page size
                const offset = (currentPage.value - 1) * pageSize.value;

                // Prepare API parameters
                const params = {
                    username: filters.username,
                    conversation_id: filters.conversation_id,
                    content_search: filters.content_search,
                    bad_case_filter: filters.bad_case_filter,
                    start_date: startDate.value,
                    end_date: endDate.value,
                    limit: pageSize.value,
                    offset: offset
                };

                // Call API service
                const result = await fetchConversations(params);

                // Update state with API response
                conversations.value = result.conversations;
                totalCount.value = result.totalCount;
            } catch (err) {
                console.error('Error loading conversations:', err);
                error.value = '加载会话数据失败，请稍后重试';
                conversations.value = {};
                totalCount.value = 0;
            } finally {
                isLoading.value = false;
            }
        };

        // Apply filters
        const applyFilters = () => {
            loadConversations();
        };

        // Format date for display
        const formatDate = (dateString) => {
            if (!dateString) {
                return '';
            }
            const [_, month, day] = dateString.split('-');
            return `${month}/${day}`;
        };

        // Handle filter change from ConversationFilters component
        const handleFilterChange = (data) => {
            // Update local state with data from the component
            filters.username = data.filters.username;
            filters.conversation_id = data.filters.conversation_id;
            filters.content_search = data.filters.content_search;
            filters.bad_case_filter = data.filters.bad_case_filter;
            startDate.value = data.startDate;
            endDate.value = data.endDate;

            // Apply the filters
            applyFilters();
        };

        // Reset filters
        const resetFilters = () => {
            filters.username = '';
            filters.conversation_id = '';
            filters.content_search = '';
            filters.bad_case_filter = 'all';
            startDate.value = '';
            endDate.value = '';
            applyFilters();
        };

        // Load data on component mount
        onMounted(() => {
            loadConversations();
        });

        // Watch for date range changes
        watch([startDate, endDate], () => {
            if (startDate.value && endDate.value) {
                applyFilters();
            }
        });

        return {
            isLoading,
            startDate,
            endDate,
            filters,
            formattedConversations,
            totalCount,
            currentPage,
            pageSize,
            applyFilters,
            resetFilters,
            formatDate,
            handlePageChange,
            handleFilterChange,
            error,
            // Responsive behavior
            isDesktopView
        };
    },
    template: `
    <div>
      <GroupSection group-id="conversations">
        <template #title>
          <div class="flex flex-col md:flex-row items-start md:items-center justify-between gap-3">
            <h2 class="text-l font-bold">会话管理</h2>
            <ConversationFilters
              :initial-filters="filters"
              :initial-start-date="startDate"
              :initial-end-date="endDate"
              @filter-change="handleFilterChange"
              @filter-reset="resetFilters"
            />
          </div>
        </template>



        <!-- Error Message -->
        <div v-if="error" class="alert alert-error mt-4">
          <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <span>{{ error }}</span>
        </div>

        <!-- Conversation Table (Now a card itself) -->
        <ConversationTableCard
          :conversations="formattedConversations"
          :is-loading="isLoading"
          :total-count="totalCount"
          :current-page="currentPage"
          :page-size="pageSize"
          @page-change="handlePageChange"
        />
      </GroupSection>
    </div>
  `
}
