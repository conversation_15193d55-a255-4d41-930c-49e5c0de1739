CREATE TABLE `area` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID，自增长',
  `area_no` int(11) DEFAULT NULL COMMENT '运营服务区编号，唯一标识一个运营服务区，如44268代表益禾堂荆州，1001代表杭州',
  `area_name` varchar(50) DEFAULT NULL COMMENT '运营服务区名称，如"益禾堂荆州"、"杭州"',
  `status` tinyint(1) DEFAULT '0' COMMENT '是否开放：0-不开放，1-开放',
  `type` int(2) DEFAULT '1' COMMENT '仓库类型：0-本部仓，1-外部仓，2-合伙人仓',
  `map_section` varchar(1000) DEFAULT NULL COMMENT '截单映射区域，格式如"浙江/杭州市/西湖区"，多个区域用逗号分隔',
  `origin_area_no` int(11) DEFAULT NULL COMMENT '代表复制自哪个运营服务区编号，为空则表示非复制出来的',
  `administrative_area` varchar(50) DEFAULT NULL COMMENT '行政区域划分，精确到市，如"湖北/荆州市"',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `support_add_order` tinyint(4) DEFAULT NULL COMMENT '是否商户支持截单后加单：0-支持，1-不支持',
  `large_area_no` int(11) DEFAULT NULL COMMENT '运营大区编号，如91表示荆州所属大区，1表示杭州所在的‘杭州大区’，关联`large_area`.`large_area_no`，大区名字为: `large_area`.`large_area_name`',
  `grade` char(2) NOT NULL DEFAULT 'S' COMMENT '区域等级：S/A/B/C/D，S为最高级',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `business_line` tinyint(4) NOT NULL DEFAULT '0' COMMENT '业务线：0-鲜沐，1-POP',
  PRIMARY KEY (`id`),
  UNIQUE KEY `index_area_name` (`area_name`),
  UNIQUE KEY `index_area_no` (`area_no`),
  KEY `idx_parent` (`parent_no`),
  KEY `idx_large_area` (`large_area_no`)
) ENGINE=InnoDB AUTO_INCREMENT=360 DEFAULT CHARSET=utf8 COMMENT='运营服务区表，这是定价的基本单元，每个sku都是按照area_no来进行不同的定价的。该表存储全国各服务城市的基础信息，支持多级区域管理和业务线区分';